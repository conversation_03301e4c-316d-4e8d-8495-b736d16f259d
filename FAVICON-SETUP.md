# Favicon Setup Guide

This document explains the comprehensive favicon setup implemented for the Navhaus website.

## 🎯 What Was Fixed

Based on the Real Favicon Generator scan, we addressed these issues:

1. ✅ **Added SVG favicon** - Modern browsers now get the crisp SVG version
2. ✅ **Added desktop PNG favicons** - Multiple sizes (16x16, 32x32, 48x48)
3. ✅ **Fixed ICO favicon** - Proper ICO file with standard sizes
4. ✅ **Added web app manifest** - PWA support with proper icons
5. ✅ **Complete HTML declarations** - All necessary meta tags and links

## 📁 Generated Files

The following favicon files were automatically generated in the `public/` directory:

```
public/
├── favicon.ico              # Multi-size ICO file (16x16, 32x32, 48x48)
├── favicon-16x16.png        # Small desktop icon
├── favicon-32x32.png        # Standard desktop icon
├── favicon-48x48.png        # Large desktop icon
├── favicon-96x96.png        # Extra large desktop icon
├── apple-touch-icon.png     # iOS home screen icon (180x180)
├── android-chrome-192x192.png  # Android icon (192x192)
├── android-chrome-512x512.png  # Android icon (512x512)
└── site.webmanifest         # Web app manifest
```

## 🔧 HTML Implementation

The favicon setup includes these HTML declarations in `src/app/layout.tsx`:

### Next.js Metadata API
```typescript
icons: {
  icon: [
    { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
    { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    { url: '/favicon.ico', sizes: '48x48', type: 'image/x-icon' },
    { url: '/images/icon.svg', type: 'image/svg+xml' },
  ],
  shortcut: '/favicon.ico',
  apple: [
    { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
  ],
},
manifest: '/site.webmanifest',
```

### Additional HTML Head Tags
```html
<link rel="icon" type="image/svg+xml" href="/images/icon.svg" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
<link rel="manifest" href="/site.webmanifest" />
<meta name="theme-color" content="#434897" />
<meta name="msapplication-TileColor" content="#434897" />
```

## 🚀 How to Regenerate

If you need to update the favicon files (e.g., after changing the source icon):

1. **Update the source**: Replace `public/images/icon.svg` with your new icon
2. **Run the generation script**:
   ```bash
   node scripts/generate-favicons.js
   ```

This will regenerate all favicon files automatically.

## 🎨 Design Specifications

- **Source**: SVG icon at `public/images/icon.svg`
- **Theme Color**: `#434897` (Bauhaus blue)
- **Background Color**: `#f0ebde` (Brand background)
- **Transparent Background**: All PNG files have transparent backgrounds
- **Sizes**: Standard favicon sizes for maximum compatibility

## 🌐 Browser Support

This setup provides optimal favicon support for:

- ✅ Modern browsers (Chrome, Firefox, Safari, Edge) - SVG favicon
- ✅ Older browsers - ICO and PNG fallbacks
- ✅ iOS devices - Apple touch icon
- ✅ Android devices - Chrome web app icons
- ✅ PWA installations - Web app manifest
- ✅ Search engines - Proper meta tags

## 🔍 Testing

To verify the favicon setup:

1. **Browser tabs**: Check if the favicon appears in browser tabs
2. **Bookmarks**: Bookmark the site and verify the icon appears
3. **Real Favicon Generator**: Re-run the scan at https://realfavicongenerator.net/
4. **Google Search**: Check if your favicon appears in search results (may take time)

## 📱 PWA Features

The web app manifest (`site.webmanifest`) enables:

- **Add to Home Screen**: Users can install the site as a PWA
- **Theme Colors**: Consistent branding in PWA mode
- **App Icons**: High-quality icons for different screen densities
- **Display Mode**: Standalone app experience

## 🛠 Scripts

Two utility scripts are available:

- `scripts/generate-favicons.js` - Generates all favicon files from SVG/PNG source
- `scripts/create-ico.js` - Creates proper multi-size ICO file with embedded 16x16, 32x32, and 48x48 sizes

Both scripts use Sharp for high-quality image processing and ico-endec for proper ICO file creation.
