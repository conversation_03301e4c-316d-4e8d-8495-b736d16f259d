/* Critical CSS - Above the fold content only */
/* This file contains only the essential styles needed for initial render */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-family: var(--font-barlow), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-display: swap;
  scroll-behavior: smooth;
}

body {
  color: #000;
  background-color: #f0ebde;
  font-family: var(--font-barlow), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  line-height: 1.6;
  overflow-x: hidden;
}

/* Ensure all sections contain their animated elements */
section {
  overflow-x: hidden;
  position: relative;
}

/* Specific containment for sections with animated elements */
.animated-section {
  overflow-x: hidden;
  position: relative;
}

/* Critical hero section styles */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 2rem 2rem;
  position: relative;
}

.text-hero {
  font-size: 4rem;
  line-height: 1.1;
  letter-spacing: -0.02em;
  font-weight: 700;
  font-family: var(--font-barlow), sans-serif;
  contain: layout style paint;
  margin: 0;
  text-align: center;
  max-width: 1200px;
}

/* Critical navigation styles */
.nav-critical {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: #f0ebde;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  backdrop-filter: blur(10px);
}

.nav-logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: #000;
  text-decoration: none;
}

.nav-menu {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-link {
  color: #000;
  text-decoration: none;
  font-weight: 500;
  transition: opacity 0.2s ease;
}

.nav-link:hover {
  opacity: 0.7;
}

/* Critical button styles */
.btn-primary {
  display: inline-block;
  padding: 0.875rem 1.5rem;
  border: 2px solid #000;
  background-color: transparent;
  color: #000;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-decoration: none;
  border-radius: 1rem;
  transition: all 0.2s ease;
  cursor: pointer;
  font-family: inherit;
}

.btn-primary:hover {
  background-color: #000;
  color: #f0ebde;
}

/* Critical responsive styles */
@media (max-width: 768px) {
  .text-hero {
    font-size: 2.5rem;
  }
  
  .nav-critical {
    padding: 0 1rem;
  }
  
  .nav-menu {
    gap: 1rem;
  }
  
  .hero-section {
    padding: 80px 1rem 2rem;
  }
}

@media (max-width: 640px) {
  .text-hero {
    font-size: 2rem;
  }
  
  .nav-menu {
    display: none; /* Hide on mobile, show hamburger menu */
  }
}

/* Critical layout utilities */
.container {
  max-width: 80rem;
  margin: 0 auto;
  padding: 0 1rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.font-bold {
  font-weight: 700;
}

.uppercase {
  text-transform: uppercase;
}

.tracking-wide {
  letter-spacing: 0.05em;
}

/* Prevent layout shift */
.loading-placeholder {
  opacity: 0;
  animation: fadeIn 0.3s ease-in-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Critical geometric shapes for hero */
.hero-shape {
  position: absolute;
  pointer-events: none;
  opacity: 0.8;
}

.hero-shape-circle {
  border-radius: 50%;
  background-color: #434897;
}

.hero-shape-square {
  background-color: #ffc527;
}

.hero-shape-triangle {
  width: 0;
  height: 0;
  background-color: transparent;
  border-style: solid;
  border-color: #e94436 transparent transparent transparent;
}

/* Hide non-critical content initially */
.defer-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.defer-load.loaded {
  opacity: 1;
  transform: translateY(0);
}
