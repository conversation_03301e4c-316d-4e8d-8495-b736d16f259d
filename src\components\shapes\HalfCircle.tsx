interface HalfCircleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  direction?: 'top' | 'bottom' | 'left' | 'right'
  className?: string
}

const sizeClasses = {
  sm: 'w-8 h-4',
  md: 'w-16 h-8',
  lg: 'w-24 h-12', 
  xl: 'w-32 h-16'
}

const colorClasses = {
  red: 'bg-bauhaus-red',
  yellow: 'bg-bauhaus-yellow',
  blue: 'bg-bauhaus-blue',
  black: 'bg-bauhaus-black',
  white: 'bg-bauhaus-white border border-bauhaus-black'
}

const directionClasses = {
  top: 'rounded-t-full',
  bottom: 'rounded-b-full', 
  left: 'rounded-l-full',
  right: 'rounded-r-full'
}

const getRotationClasses = (direction: string) => {
  switch (direction) {
    case 'left':
      return 'w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32'
    case 'right':
      return 'w-4 h-8 md:w-8 md:h-16 lg:w-12 lg:h-24 xl:w-16 xl:h-32'
    default:
      return ''
  }
}

export default function HalfCircle({ 
  size = 'md',
  color = 'red',
  direction = 'top', 
  className = ''
}: HalfCircleProps) {
  const rotationClasses = getRotationClasses(direction)
  const finalSizeClasses = rotationClasses || sizeClasses[size]
  
  return (
    <div 
      className={`${finalSizeClasses} ${colorClasses[color]} ${directionClasses[direction]} ${className}`}
    />
  )
}
