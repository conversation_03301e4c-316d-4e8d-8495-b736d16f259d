interface TriangleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  direction?: 'up' | 'down' | 'left' | 'right'
  className?: string
}

const sizeClasses = {
  sm: 'w-0 h-0 border-l-[16px] border-r-[16px] border-b-[28px]',
  md: 'w-0 h-0 border-l-[24px] border-r-[24px] border-b-[42px]',
  lg: 'w-0 h-0 border-l-[32px] border-r-[32px] border-b-[56px]',
  xl: 'w-0 h-0 border-l-[48px] border-r-[48px] border-b-[84px]'
}

const getTriangleClasses = (size: string, color: string, direction: string) => {
  const colorMap = {
    red: '#e94436',
    yellow: '#ffc527',
    blue: '#434897',
    black: '#000000',
    white: '#ffffff'
  }
  
  const baseSize = sizeClasses[size as keyof typeof sizeClasses]
  const triangleColor = colorMap[color as keyof typeof colorMap]
  
  switch (direction) {
    case 'up':
      return `${baseSize} border-l-transparent border-r-transparent`
    case 'down':
      return baseSize.replace('border-b-', 'border-t-') + ' border-l-transparent border-r-transparent'
    case 'left':
      return baseSize.replace('border-l-', 'border-r-').replace('border-r-', 'border-t-').replace('border-b-', 'border-l-') + ' border-t-transparent border-b-transparent'
    case 'right':
      return baseSize.replace('border-r-', 'border-l-').replace('border-l-', 'border-t-').replace('border-b-', 'border-r-') + ' border-t-transparent border-b-transparent'
    default:
      return `${baseSize} border-l-transparent border-r-transparent`
  }
}

export default function Triangle({ 
  size = 'md',
  color = 'yellow', 
  direction = 'up',
  className = ''
}: TriangleProps) {
  const triangleClasses = getTriangleClasses(size, color, direction)
  
  const style = {
    borderBottomColor: direction === 'up' ? (color === 'red' ? '#e94436' : color === 'yellow' ? '#ffc527' : color === 'blue' ? '#434897' : color === 'black' ? '#000000' : '#ffffff') : 'transparent',
    borderTopColor: direction === 'down' ? (color === 'red' ? '#e94436' : color === 'yellow' ? '#ffc527' : color === 'blue' ? '#434897' : color === 'black' ? '#000000' : '#ffffff') : 'transparent',
    borderLeftColor: direction === 'right' ? (color === 'red' ? '#e94436' : color === 'yellow' ? '#ffc527' : color === 'blue' ? '#434897' : color === 'black' ? '#000000' : '#ffffff') : 'transparent',
    borderRightColor: direction === 'left' ? (color === 'red' ? '#e94436' : color === 'yellow' ? '#ffc527' : color === 'blue' ? '#434897' : color === 'black' ? '#000000' : '#ffffff') : 'transparent'
  }
  
  return (
    <div 
      className={`${triangleClasses} ${className}`}
      style={style}
    />
  )
}
