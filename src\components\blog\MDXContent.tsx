interface MDXContentProps {
  content: string
}

export default function MDXContent({ content }: MDXContentProps) {
  return (
    <article className="blog-content">
      <div
        className="prose prose-xl max-w-none
          prose-headings:font-bold prose-headings:text-bauhaus-black prose-headings:tracking-tight
          prose-h1:text-4xl prose-h1:leading-tight prose-h1:mb-8 prose-h1:mt-12
          prose-h2:text-3xl prose-h2:leading-tight prose-h2:mb-6 prose-h2:mt-10 prose-h2:border-b prose-h2:border-bauhaus-black prose-h2:pb-3
          prose-h3:text-2xl prose-h3:leading-snug prose-h3:mb-4 prose-h3:mt-8
          prose-h4:text-xl prose-h4:leading-snug prose-h4:mb-3 prose-h4:mt-6
          prose-p:text-bauhaus-black prose-p:leading-relaxed prose-p:mb-6 prose-p:text-lg
          prose-a:text-bauhaus-blue prose-a:no-underline prose-a:font-medium prose-a:border-b prose-a:border-bauhaus-blue
          hover:prose-a:text-bauhaus-red hover:prose-a:border-bauhaus-red
          prose-strong:text-bauhaus-black prose-strong:font-bold
          prose-em:text-bauhaus-black prose-em:italic
          prose-code:bg-bauhaus-yellow prose-code:text-bauhaus-black prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm prose-code:font-mono
          prose-pre:bg-bauhaus-black prose-pre:text-bauhaus-white prose-pre:rounded-xl prose-pre:p-6 prose-pre:overflow-x-auto
          prose-blockquote:border-l-4 prose-blockquote:border-bauhaus-blue prose-blockquote:pl-6 prose-blockquote:ml-0 prose-blockquote:italic prose-blockquote:text-bauhaus-black prose-blockquote:bg-bauhaus-yellow prose-blockquote:py-4 prose-blockquote:rounded-r-lg
          [&_.quote-caption]:not-italic [&_.quote-caption]:text-sm [&_.quote-caption]:text-bauhaus-black [&_.quote-caption]:mt-3 [&_.quote-caption]:opacity-80
          [&_.quote-with-caption]:pb-2
          prose-ul:my-6 prose-ul:space-y-2
          prose-ol:my-6 prose-ol:space-y-2
          prose-li:text-bauhaus-black prose-li:leading-relaxed prose-li:text-lg
          prose-table:border-collapse prose-table:border prose-table:border-bauhaus-black prose-table:rounded-lg prose-table:overflow-hidden
          prose-thead:bg-bauhaus-blue
          prose-th:border prose-th:border-bauhaus-black prose-th:px-4 prose-th:py-3 prose-th:text-left prose-th:font-bold prose-th:text-bauhaus-white
          prose-td:border prose-td:border-bauhaus-black prose-td:px-4 prose-td:py-3 prose-td:text-bauhaus-black
          prose-img:rounded-xl prose-img:shadow-sm"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    </article>
  )
}
