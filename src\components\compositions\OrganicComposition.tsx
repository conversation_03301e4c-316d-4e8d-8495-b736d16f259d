'use client'

import { <PERSON>ed<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>lo<PERSON>, HalfCircle, QuarterCircle, SoftGrid } from '../shapes/RoundedShapes';
import { SoftCircle } from '../shapes/Circle';
import { RoundedRectangle, PillRectangle } from '../shapes/Rectangle';
import {
  AnimatedSoftGrid,
  AnimatedSoftCircle,
  AnimatedBlob,
  AnimatedHalfCircle,
  AnimatedQuarterCircle,
  AnimatedPill,
  AnimatedRoundedRect
} from '../shapes/AnimatedShapes';
import { useEffect, useState } from 'react';

interface OrganicCompositionProps {
  variant?: 'hero' | 'feature' | 'minimal';
  className?: string;
}

// Static hero composition for immediate render (LCP optimization)
export function StaticHeroComposition({ className = '' }: { className?: string }) {
  return (
    <div className={`relative ${className}`}>
      {/* Background grid overlay - static */}
      <SoftGrid className="text-black h-full" opacity="hero" />

      {/* Large background shapes - static */}
      <div className="absolute top-0 right-0 w-64 h-64 opacity-80">
        <HalfCircle
          color="blue"
          direction="right"
          className="w-full h-full"
        />
      </div>

      <div className="absolute bottom-0 left-0 w-48 h-48">
        <QuarterCircle
          color="yellow"
          corner="bottom-left"
          className="w-full h-full"
        />
      </div>

      {/* Floating organic shapes - static */}
      <div className="absolute top-1/4 left-1/3 w-24 h-24 opacity-90">
        <Blob
          color="red"
          className="w-full h-full"
        />
      </div>

      <div className="absolute top-1/2 right-1/4 w-20 h-16">
        <SoftCircle
          size="lg"
          color="yellow"
          className="h-full"
        />
      </div>

      {/* Small accent shapes - static */}
      <div className="absolute bottom-1/3 left-1/4 w-16 h-6">
        <Pill
          color="black"
          className="w-full h-full"
        />
      </div>

      <div className="absolute top-3/4 right-1/3 w-12 h-12">
        <RoundedRect
          color="blue"
          className="w-full h-full"
        />
      </div>
    </div>
  );
}

// Hero composition with progressive enhancement for LCP optimization
export function HeroComposition({ className = '' }: { className?: string }) {
  const [isAnimated, setIsAnimated] = useState(false);

  useEffect(() => {
    // Defer animations until after initial render
    const timer = setTimeout(() => {
      setIsAnimated(true);
    }, 100); // Small delay to ensure LCP is captured first

    return () => clearTimeout(timer);
  }, []);

  if (!isAnimated) {
    return <StaticHeroComposition className={className} />;
  }

  return (
    <div className={`relative ${className}`}>
      {/* Background grid overlay */}
      <AnimatedSoftGrid className="text-black h-full" opacity="hero" animationPreset="subtle" animationIndex={0} />

      {/* Large background shapes */}
      <div className="absolute top-0 right-0 w-64 h-64 opacity-80">
        <AnimatedHalfCircle
          color="blue"
          direction="right"
          className="w-full h-full"
          animationPreset="gentle"
          animationIndex={1}
        />
      </div>

      <div className="absolute bottom-0 left-0 w-48 h-48">
        <AnimatedQuarterCircle
          color="yellow"
          corner="bottom-left"
          className="w-full h-full"
          animationPreset="flowing"
          animationIndex={2}
        />
      </div>

      {/* Floating organic shapes */}
      <div className="absolute top-1/4 left-1/3 w-24 h-24 opacity-90">
        <AnimatedBlob
          color="red"
          className="w-full h-full"
          animationPreset="dynamic"
          animationIndex={3}
        />
      </div>

      <div className="absolute top-1/2 right-1/4 w-20 h-16">
        <AnimatedSoftCircle
          size="lg"
          color="yellow"
          className="h-full"
          animationPreset="energetic"
          animationIndex={4}
        />
      </div>

      {/* Small accent shapes */}
      <div className="absolute bottom-1/3 left-1/4 w-16 h-6">
        <AnimatedPill
          color="black"
          className="w-full h-full"
          animationPreset="horizontal"
          animationIndex={5}
        />
      </div>

      <div className="absolute top-3/4 right-1/3 w-12 h-12">
        <AnimatedRoundedRect
          color="blue"
          className="w-full h-full"
          animationPreset="pulse"
          animationIndex={6}
        />
      </div>
    </div>
  );
}

// Feature composition inspired by Collabo reference
export function FeatureComposition({ className = '' }: { className?: string }) {
  return (
    <div className={`relative ${className}`}>
      {/* Main geometric arrangement */}
      <div className="flex items-center justify-center space-x-8">
        <SoftCircle size="xl" color="blue" />
        
        <div className="flex flex-col space-y-4">
          <RoundedRectangle width="lg" height="xl" color="yellow" />
          <SoftCircle size="lg" color="black" />
        </div>
        
        <QuarterCircle 
          color="red" 
          corner="top-right"
          className="w-32 h-32"
        />
      </div>
      
      {/* Floating accent elements */}
      <HalfCircle 
        color="blue" 
        direction="left"
        className="absolute -top-8 -right-8 w-24 h-24 opacity-60"
      />
    </div>
  );
}

// Minimal composition inspired by Horizon reference
export function MinimalComposition({ className = '' }: { className?: string }) {
  return (
    <div className={`relative ${className}`}>
      {/* Clean geometric grid */}
      <div className="grid grid-cols-3 gap-8 items-center">
        <div className="flex flex-col space-y-4">
          <SoftCircle size="md" color="black" />
          <Pill color="yellow" className="w-20 h-8" />
        </div>
        
        <div className="relative">
          <RoundedRect color="red" className="w-24 h-24" />
          <SoftCircle 
            size="sm" 
            color="black" 
            className="absolute -bottom-2 -right-2"
          />
        </div>
        
        <Blob color="blue" className="w-20 h-20" />
      </div>
      
      {/* Subtle connecting lines */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none opacity-20">
        <path 
          d="M 50 50 Q 150 100 250 50" 
          stroke="currentColor" 
          strokeWidth="1" 
          fill="none"
          className="text-black"
        />
      </svg>
    </div>
  );
}

// Main component that switches between variants
export default function OrganicComposition({ 
  variant = 'hero', 
  className = '' 
}: OrganicCompositionProps) {
  switch (variant) {
    case 'hero':
      return <HeroComposition className={className} />;
    case 'feature':
      return <FeatureComposition className={className} />;
    case 'minimal':
      return <MinimalComposition className={className} />;
    default:
      return <HeroComposition className={className} />;
  }
}
