/** @type {import('next').NextConfig} */
const nextConfig = {
  // Production optimizations
  // output: 'standalone', // Uncomment for Docker/serverless deployment
  compress: true, // Enable gzip compression
  poweredByHeader: false, // Remove X-Powered-By header for security

  // Build optimizations to prevent stack overflow
  experimental: {
    // Reduce build trace collection complexity
    optimizePackageImports: ['react', 'react-dom'],
  },

  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },

  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          }
        ]
      }
    ]
  },

  // Redirects for domain consistency (if needed)
  async redirects() {
    return [
      // Add any redirects here if needed
      // Example: redirect www to non-www
      // {
      //   source: '/:path*',
      //   has: [{ type: 'host', value: 'www.navhaus.com' }],
      //   destination: 'https://navhaus.com/:path*',
      //   permanent: true,
      // },
    ]
  }
}

module.exports = nextConfig
