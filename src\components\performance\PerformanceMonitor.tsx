'use client'

import { useEffect } from 'react'

export default function PerformanceMonitor() {
  useEffect(() => {
    // Only run in development or when explicitly enabled
    if (process.env.NODE_ENV !== 'development') return

    // Monitor LCP
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      
      if (lastEntry) {
        console.log('🚀 LCP:', Math.round(lastEntry.startTime), 'ms')
        console.log('LCP Element:', (lastEntry as any).element)
      }
    })

    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      console.log('LCP monitoring not supported')
    }

    // Monitor CLS
    const clsObserver = new PerformanceObserver((list) => {
      let clsValue = 0
      for (const entry of list.getEntries()) {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value
        }
      }
      if (clsValue > 0) {
        console.log('📐 CLS:', clsValue.toFixed(4))
      }
    })

    try {
      clsObserver.observe({ entryTypes: ['layout-shift'] })
    } catch (e) {
      console.log('CLS monitoring not supported')
    }

    return () => {
      observer.disconnect()
      clsObserver.disconnect()
    }
  }, [])

  return null
}
