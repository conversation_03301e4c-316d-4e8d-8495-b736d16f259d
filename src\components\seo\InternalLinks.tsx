import Link from 'next/link'

interface InternalLinksProps {
  variant?: 'footer' | 'sidebar' | 'inline'
  className?: string
}

export default function InternalLinks({ variant = 'footer', className = '' }: InternalLinksProps) {
  const links = {
    services: [
      { href: '/services', label: 'Web Development Services', keywords: 'custom wordpress website, wordpress agency, web development, react development' },
    ],
    pages: [
      { href: '/about', label: 'About Our WordPress Agency', keywords: 'wordpress agency team, web development team' },
      { href: '/contact', label: 'Contact WordPress Developers', keywords: 'hire wordpress developers, wordpress project quote' },
      { href: '/blog', label: 'WordPress & Web Development Blog', keywords: 'wordpress blog, web development insights' },
    ],
    blog: [
      { href: '/blog/custom-wordpress-themes-sage-blade', label: 'Custom WordPress Themes with Sage', keywords: 'wordpress themes, sage wordpress' },
      { href: '/blog/wordpress-performance-optimization-guide', label: 'WordPress Performance Optimization', keywords: 'wordpress performance, website speed' },
    ]
  }

  if (variant === 'footer') {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-3 gap-8 ${className}`}>
        <div>
          <h3 className="font-bold text-lg mb-4">Services</h3>
          <ul className="space-y-2">
            {links.services.map((link) => (
              <li key={link.href}>
                <Link 
                  href={link.href} 
                  className="text-gray-300 hover:text-brand-background transition-colors"
                  title={link.keywords}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-bold text-lg mb-4">Company</h3>
          <ul className="space-y-2">
            {links.pages.map((link) => (
              <li key={link.href}>
                <Link 
                  href={link.href} 
                  className="text-gray-300 hover:text-brand-background transition-colors"
                  title={link.keywords}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-bold text-lg mb-4">Resources</h3>
          <ul className="space-y-2">
            {links.blog.map((link) => (
              <li key={link.href}>
                <Link 
                  href={link.href} 
                  className="text-gray-300 hover:text-brand-background transition-colors"
                  title={link.keywords}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )
  }

  if (variant === 'sidebar') {
    return (
      <div className={`space-y-6 ${className}`}>
        <div>
          <h3 className="font-bold text-lg mb-3">Our Services</h3>
          <ul className="space-y-2">
            {links.services.map((link) => (
              <li key={link.href}>
                <Link 
                  href={link.href} 
                  className="text-bauhaus-blue hover:text-bauhaus-red transition-colors text-sm"
                  title={link.keywords}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h3 className="font-bold text-lg mb-3">Popular Articles</h3>
          <ul className="space-y-2">
            {links.blog.map((link) => (
              <li key={link.href}>
                <Link 
                  href={link.href} 
                  className="text-bauhaus-blue hover:text-bauhaus-red transition-colors text-sm"
                  title={link.keywords}
                >
                  {link.label}
                </Link>
              </li>
            ))}
          </ul>
        </div>
      </div>
    )
  }

  // Inline variant for contextual linking
  return (
    <div className={`space-y-4 ${className}`}>
      <h3 className="font-bold text-lg">Related Services</h3>
      <div className="flex flex-wrap gap-2">
        {links.services.map((link) => (
          <Link 
            key={link.href}
            href={link.href} 
            className="bg-bauhaus-blue text-white px-3 py-1 rounded-full text-sm hover:bg-bauhaus-red transition-colors"
            title={link.keywords}
          >
            {link.label}
          </Link>
        ))}
      </div>
    </div>
  )
}
