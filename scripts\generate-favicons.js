/**
 * Favicon Generation Script
 *
 * This script generates all required favicon files from your SVG icon.
 * Run: node scripts/generate-favicons.js
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

const sizes = [
  { name: 'favicon-16x16.png', size: 16 },
  { name: 'favicon-32x32.png', size: 32 },
  { name: 'favicon-48x48.png', size: 48 },
  { name: 'favicon-96x96.png', size: 96 },
  { name: 'apple-touch-icon.png', size: 180 },
  { name: 'android-chrome-192x192.png', size: 192 },
  { name: 'android-chrome-512x512.png', size: 512 },
];

async function generateFavicons() {
  const svgPath = path.join(__dirname, '../public/images/icon.svg');
  const pngPath = path.join(__dirname, '../public/images/icon.png');
  const publicDir = path.join(__dirname, '../public');

  // Try SVG first, fallback to PNG
  let sourcePath = svgPath;
  if (!fs.existsSync(svgPath)) {
    if (fs.existsSync(pngPath)) {
      sourcePath = pngPath;
      console.log('Using PNG source:', pngPath);
    } else {
      console.error('Neither SVG nor PNG icon found');
      return;
    }
  } else {
    console.log('Using SVG source:', svgPath);
  }

  console.log('Generating favicon files...');

  try {
    // Generate PNG files
    for (const { name, size } of sizes) {
      const outputPath = path.join(publicDir, name);
      await sharp(sourcePath)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 }
        })
        .png()
        .toFile(outputPath);
      console.log(`✓ Generated ${name} (${size}x${size})`);
    }

    // Create proper multi-size ICO file
    console.log('Creating multi-size ICO file...');
    const createIco = require('./create-ico.js');
    await createIco();

    console.log('\n🎉 All favicon files generated successfully!');
    console.log('\nGenerated files:');
    sizes.forEach(({ name }) => console.log(`  - ${name}`));
    console.log('  - favicon.ico (multi-size: 16x16, 32x32, 48x48)');

  } catch (error) {
    console.error('Error generating favicons:', error);
  }
}

if (require.main === module) {
  generateFavicons();
}

module.exports = generateFavicons;
