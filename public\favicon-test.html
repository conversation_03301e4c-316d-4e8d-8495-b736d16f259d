<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon Test - Navhaus</title>
    
    <!-- Favicon declarations -->
    <link rel="icon" type="image/svg+xml" href="/images/icon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="manifest" href="/site.webmanifest" />
    <meta name="theme-color" content="#434897" />
    <meta name="msapplication-TileColor" content="#434897" />
    
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0ebde;
        }
        .favicon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .favicon-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .favicon-item img {
            max-width: 64px;
            max-height: 64px;
            margin-bottom: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Favicon Test Page</h1>
    <p>This page tests all favicon files and declarations for the Navhaus website.</p>
    
    <h2>Favicon Files</h2>
    <div class="favicon-grid">
        <div class="favicon-item">
            <img src="/images/icon.svg" alt="SVG Icon" />
            <h3>SVG Icon</h3>
            <p>Vector format for modern browsers</p>
        </div>
        
        <div class="favicon-item">
            <img src="/favicon-16x16.png" alt="16x16 PNG" />
            <h3>16x16 PNG</h3>
            <p>Small desktop icon</p>
        </div>
        
        <div class="favicon-item">
            <img src="/favicon-32x32.png" alt="32x32 PNG" />
            <h3>32x32 PNG</h3>
            <p>Standard desktop icon</p>
        </div>
        
        <div class="favicon-item">
            <img src="/favicon-48x48.png" alt="48x48 PNG" />
            <h3>48x48 PNG</h3>
            <p>Large desktop icon</p>
        </div>

        <div class="favicon-item">
            <img src="/favicon-96x96.png" alt="96x96 PNG" />
            <h3>96x96 PNG</h3>
            <p>Extra large desktop icon</p>
        </div>
        
        <div class="favicon-item">
            <img src="/apple-touch-icon.png" alt="Apple Touch Icon" />
            <h3>Apple Touch Icon</h3>
            <p>180x180 for iOS devices</p>
        </div>
        
        <div class="favicon-item">
            <img src="/android-chrome-192x192.png" alt="Android Chrome 192" />
            <h3>Android Chrome 192</h3>
            <p>192x192 for Android</p>
        </div>
    </div>
    
    <h2>Test Results</h2>
    <div id="test-results"></div>
    
    <h2>Instructions</h2>
    <ol>
        <li><strong>Check browser tab</strong> - You should see the Navhaus favicon in the browser tab</li>
        <li><strong>Bookmark this page</strong> - The favicon should appear in your bookmarks</li>
        <li><strong>Check mobile</strong> - Add to home screen on mobile devices</li>
        <li><strong>Run Real Favicon Generator</strong> - Test at https://realfavicongenerator.net/</li>
    </ol>
    
    <script>
        // Test if favicon files are accessible
        const faviconFiles = [
            '/images/icon.svg',
            '/favicon-16x16.png',
            '/favicon-32x32.png',
            '/favicon-48x48.png',
            '/favicon-96x96.png',
            '/favicon.ico',
            '/apple-touch-icon.png',
            '/android-chrome-192x192.png',
            '/android-chrome-512x512.png',
            '/site.webmanifest'
        ];
        
        const resultsDiv = document.getElementById('test-results');
        
        faviconFiles.forEach(file => {
            fetch(file)
                .then(response => {
                    const status = response.ok ? 'success' : 'error';
                    const message = response.ok ? 'Found' : `Error ${response.status}`;
                    resultsDiv.innerHTML += `<div class="status ${status}">${file}: ${message}</div>`;
                })
                .catch(error => {
                    resultsDiv.innerHTML += `<div class="status error">${file}: Failed to load</div>`;
                });
        });
    </script>
</body>
</html>
