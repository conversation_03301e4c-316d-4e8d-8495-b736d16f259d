interface RectangleProps {
  width?: 'sm' | 'md' | 'lg' | 'xl'
  height?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  className?: string
}

const widthClasses = {
  sm: 'w-12',
  md: 'w-24',
  lg: 'w-32', 
  xl: 'w-48'
}

const heightClasses = {
  sm: 'h-8',
  md: 'h-16',
  lg: 'h-24',
  xl: 'h-32'
}

const colorClasses = {
  red: 'bg-brand-red',
  yellow: 'bg-brand-yellow',
  blue: 'bg-brand-blue',
  black: 'bg-black',
  white: 'bg-white border border-black'
}

export default function Rectangle({
  width = 'md',
  height = 'md',
  color = 'blue',
  className = ''
}: RectangleProps) {
  return (
    <div
      className={`${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`}
    />
  )
}

// Rounded Rectangle (inspired by logo's soft corners)
export function RoundedRectangle({
  width = 'md',
  height = 'md',
  color = 'blue',
  className = ''
}: RectangleProps) {
  return (
    <div
      className={`rounded-3xl ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`}
    />
  )
}

// Pill shape (very rounded rectangle)
export function PillRectangle({
  width = 'md',
  height = 'md',
  color = 'yellow',
  className = ''
}: RectangleProps) {
  return (
    <div
      className={`rounded-full ${widthClasses[width]} ${heightClasses[height]} ${colorClasses[color]} ${className}`}
    />
  )
}
