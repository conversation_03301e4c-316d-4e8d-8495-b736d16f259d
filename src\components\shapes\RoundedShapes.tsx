interface RoundedShapeProps {
  className?: string;
  color?: 'red' | 'yellow' | 'blue' | 'black';
}

// Rounded Rectangle with soft corners
export function RoundedRect({ className = '', color = 'blue' }: RoundedShapeProps) {
  const colorClasses = {
    red: 'bg-brand-red',
    yellow: 'bg-brand-yellow', 
    blue: 'bg-brand-blue',
    black: 'bg-black'
  };

  return (
    <div className={`${colorClasses[color]} rounded-3xl ${className}`} />
  );
}

// Pill shape (very rounded rectangle)
export function Pill({ className = '', color = 'yellow' }: RoundedShapeProps) {
  const colorClasses = {
    red: 'bg-brand-red',
    yellow: 'bg-brand-yellow',
    blue: 'bg-brand-blue', 
    black: 'bg-black'
  };

  return (
    <div className={`${colorClasses[color]} rounded-full ${className}`} />
  );
}

// Organic blob shape using CSS
export function Blob({ className = '', color = 'red' }: RoundedShapeProps) {
  const colorClasses = {
    red: 'bg-brand-red',
    yellow: 'bg-brand-yellow',
    blue: 'bg-brand-blue',
    black: 'bg-black'
  };

  return (
    <div 
      className={`${colorClasses[color]} ${className}`}
      style={{
        borderRadius: '60% 40% 30% 70% / 60% 30% 70% 40%',
      }}
    />
  );
}

// Half circle (like in your references)
export function HalfCircle({ className = '', color = 'blue', direction = 'right' }: RoundedShapeProps & { direction?: 'left' | 'right' | 'top' | 'bottom' }) {
  const colorClasses = {
    red: 'bg-brand-red',
    yellow: 'bg-brand-yellow',
    blue: 'bg-brand-blue',
    black: 'bg-black'
  };

  const directionClasses = {
    right: 'rounded-l-full',
    left: 'rounded-r-full', 
    top: 'rounded-b-full',
    bottom: 'rounded-t-full'
  };

  return (
    <div className={`${colorClasses[color]} ${directionClasses[direction]} ${className}`} />
  );
}

// Quarter circle
export function QuarterCircle({ className = '', color = 'yellow', corner = 'top-left' }: RoundedShapeProps & { corner?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' }) {
  const colorClasses = {
    red: 'bg-brand-red',
    yellow: 'bg-brand-yellow',
    blue: 'bg-brand-blue',
    black: 'bg-black'
  };

  const cornerClasses = {
    'top-left': 'rounded-br-full',
    'top-right': 'rounded-bl-full',
    'bottom-left': 'rounded-tr-full', 
    'bottom-right': 'rounded-tl-full'
  };

  return (
    <div className={`${colorClasses[color]} ${cornerClasses[corner]} ${className}`} />
  );
}

// Rounded square with cutout (inspired by your logo)
export function RoundedSquareWithCutout({ className = '', color = 'black' }: RoundedShapeProps) {
  const colorClasses = {
    red: 'bg-brand-red',
    yellow: 'bg-brand-yellow',
    blue: 'bg-brand-blue',
    black: 'bg-black'
  };

  return (
    <div className={`relative ${className}`}>
      <div className={`${colorClasses[color]} rounded-3xl w-full h-full`} />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-brand-background rounded-full w-1/2 h-1/2" />
    </div>
  );
}

// Soft grid overlay (like in the DSM reference)
export function SoftGrid({ className = '', opacity = 'default' }: { className?: string; opacity?: 'default' | 'hero' }) {
  const opacityClass = opacity === 'hero' ? 'opacity-40' : 'opacity-20';
  const strokeOpacity = opacity === 'hero' ? '0.5' : '0.3';

  return (
    <div className={`absolute inset-0 ${opacityClass} ${className}`}>
      <svg width="100%" height="100%" className="w-full h-full">
        <defs>
          <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="currentColor" strokeWidth="1" opacity={strokeOpacity}/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
      </svg>
    </div>
  );
}
