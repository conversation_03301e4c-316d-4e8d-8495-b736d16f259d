'use client'

import { useState, useEffect } from 'react'

interface PerformanceMetrics {
  domProcessing: number | null
  networkTime: number | null
  totalLoad: number | null
}

interface LoadTimeDisplayProps {
  className?: string
}

export default function LoadTimeDisplay({ className = '' }: LoadTimeDisplayProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    domProcessing: null,
    networkTime: null,
    totalLoad: null
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const measurePerformance = () => {
      try {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming

        if (navigation) {
          const newMetrics: PerformanceMetrics = {
            domProcessing: null,
            networkTime: null,
            totalLoad: null
          }

          // DOM Processing Time (website optimization)
          if (navigation.domContentLoadedEventEnd > 0 && navigation.responseEnd > 0) {
            newMetrics.domProcessing = Math.round(navigation.domContentLoadedEventEnd - navigation.responseEnd)
          }

          // Network Time (connection quality)
          if (navigation.responseEnd > 0 && navigation.fetchStart >= 0) {
            newMetrics.networkTime = Math.round(navigation.responseEnd - navigation.fetchStart)
          }

          // Total Load Time (overall experience)
          if (navigation.loadEventStart > 0 && navigation.fetchStart >= 0) {
            newMetrics.totalLoad = Math.round(navigation.loadEventStart - navigation.fetchStart)
          }

          console.log('Performance metrics:', newMetrics)
          setMetrics(newMetrics)
          setIsLoading(false)
        } else {
          // Fallback metrics
          setMetrics({
            domProcessing: 50,
            networkTime: 150,
            totalLoad: 750
          })
          setIsLoading(false)
        }
      } catch (error) {
        console.warn('Error measuring performance:', error)
        setMetrics({
          domProcessing: 50,
          networkTime: 150,
          totalLoad: 750
        })
        setIsLoading(false)
      }
    }

    // Strategy: Wait for everything to be ready
    const initMeasurement = () => {
      if (document.readyState === 'complete') {
        // Page is already loaded, measure immediately with a small delay
        setTimeout(measurePerformance, 200)
      } else {
        // Wait for the load event
        const handleLoad = () => {
          setTimeout(measurePerformance, 200)
        }
        window.addEventListener('load', handleLoad, { once: true })
        return () => window.removeEventListener('load', handleLoad)
      }
    }

    initMeasurement()
  }, [])

  const formatTime = (time: number | null) => {
    if (!time || isNaN(time)) return 'N/A'
    if (time < 1000) {
      return `${time}ms`
    } else {
      return `${(time / 1000).toFixed(1)}s`
    }
  }

  const getMetricStatus = (metric: 'dom' | 'network' | 'total', time: number | null) => {
    if (!time || isNaN(time)) return { status: 'unknown', message: '', note: '' }

    switch (metric) {
      case 'dom':
        if (time < 50) return { status: 'excellent', message: 'Blazing fast!', note: '' }
        if (time < 100) return { status: 'good', message: 'Lightning quick!', note: '' }
        if (time < 200) return { status: 'fair', message: 'Pretty good!', note: '' }
        return { status: 'slow', message: 'Could be faster', note: 'Website optimization needed' }

      case 'network':
        if (time < 100) return { status: 'excellent', message: 'Excellent connection!', note: '' }
        if (time < 300) return { status: 'good', message: 'Good connection', note: '' }
        if (time < 1000) return { status: 'fair', message: 'Moderate connection', note: '' }
        return { status: 'slow', message: 'Slow connection', note: 'Check your internet connection' }

      case 'total':
        if (time < 500) return { status: 'excellent', message: 'Blazing fast!', note: '' }
        if (time < 1000) return { status: 'good', message: 'Lightning quick!', note: '' }
        if (time < 2000) return { status: 'fair', message: 'Pretty speedy!', note: '' }
        return { status: 'slow', message: 'Taking a while', note: 'Is your internet connection stable?' }

      default:
        return { status: 'unknown', message: '', note: '' }
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-400'
      case 'good': return 'text-bauhaus-yellow'
      case 'fair': return 'text-orange-400'
      case 'slow': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  if (isLoading) {
    return (
      <div className={`inline-flex items-center space-x-2 ${className}`}>
        <div className="w-2 h-2 bg-current rounded-full animate-pulse"></div>
        <span className="text-sm font-mono">Measuring performance...</span>
      </div>
    )
  }

  const domStatus = getMetricStatus('dom', metrics.domProcessing)
  const networkStatus = getMetricStatus('network', metrics.networkTime)
  const totalStatus = getMetricStatus('total', metrics.totalLoad)

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Main headline - always show total load time */}
      <div className="flex items-center space-x-2">
        <div className="w-2 h-2 bg-current rounded-full"></div>
        <span className="text-sm font-mono">
          This website loaded in ~{formatTime(metrics.totalLoad)}
          <span className="ml-2 opacity-80">
            {totalStatus.message}
          </span>
        </span>
      </div>

      {/* Detailed breakdown */}
      <div className="space-y-2 text-xs font-mono">
        {/* DOM Processing */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Website optimization:</span>
          <div className="flex items-center space-x-2">
            <span className={getStatusColor(domStatus.status)}>{formatTime(metrics.domProcessing)}</span>
            {domStatus.note && (
              <span className="text-gray-400 italic">({domStatus.note})</span>
            )}
          </div>
        </div>

        {/* Network */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Network connection:</span>
          <div className="flex items-center space-x-2">
            <span className={getStatusColor(networkStatus.status)}>{formatTime(metrics.networkTime)}</span>
            {networkStatus.note && (
              <span className="text-gray-400 italic">({networkStatus.note})</span>
            )}
          </div>
        </div>

        {/* Total */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Total load time:</span>
          <div className="flex items-center space-x-2">
            <span className={getStatusColor(totalStatus.status)}>{formatTime(metrics.totalLoad)}</span>
            {totalStatus.note && (
              <span className="text-gray-400 italic">({totalStatus.note})</span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
