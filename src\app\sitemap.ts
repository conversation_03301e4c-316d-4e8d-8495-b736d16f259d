import { MetadataRoute } from 'next'
import { getAllPosts, getAllCategories } from '@/lib/mdx'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://navhaus.com'
  const posts = getAllPosts()
  const categories = getAllCategories()

  const blogUrls = posts.map((post) => {
    // Calculate priority based on post features
    let priority = 0.7
    if (post.featured) priority = 0.8
    if (post.category === 'WordPress Development') priority += 0.05

    // Determine change frequency based on post age
    const postDate = new Date(post.date)
    const now = new Date()
    const daysSincePublished = Math.floor((now.getTime() - postDate.getTime()) / (1000 * 60 * 60 * 24))

    let changeFrequency: 'daily' | 'weekly' | 'monthly' | 'yearly' = 'monthly'
    if (daysSincePublished < 30) changeFrequency = 'weekly'
    else if (daysSincePublished < 90) changeFrequency = 'monthly'
    else changeFrequency = 'yearly'

    return {
      url: `${baseUrl}/blog/${post.slug}`,
      lastModified: new Date(post.date),
      changeFrequency,
      priority: Math.min(priority, 0.9), // Cap at 0.9
    }
  })

  // Generate category URLs (if you plan to add category pages in the future)
  const categoryUrls = categories.map((category) => ({
    url: `${baseUrl}/blog/category/${category.toLowerCase().replace(/\s+/g, '-')}`,
    lastModified: new Date(),
    changeFrequency: 'weekly' as const,
    priority: 0.6,
  }))

  return [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/services`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: posts.length > 0 ? new Date(Math.max(...posts.map(p => new Date(p.date).getTime()))) : new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    // Add category URLs (commented out until category pages are implemented)
    // ...categoryUrls,
    ...blogUrls,
  ]
}
