// Simple script to measure LCP improvements
// Run with: node scripts/measure-lcp.js

const puppeteer = require('puppeteer');

async function measureLCP(url = 'http://localhost:3001') {
  const browser = await puppeteer.launch({ headless: true });
  const page = await browser.newPage();
  
  // Set up performance monitoring
  await page.evaluateOnNewDocument(() => {
    window.lcpValue = 0;
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      if (lastEntry) {
        window.lcpValue = lastEntry.startTime;
      }
    });
    
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (e) {
      console.log('LCP monitoring not supported');
    }
  });

  console.log('🔍 Measuring LCP for:', url);
  
  const startTime = Date.now();
  await page.goto(url, { waitUntil: 'networkidle0' });
  
  // Wait a bit for LCP to be captured
  await page.waitForTimeout(2000);
  
  const lcpValue = await page.evaluate(() => window.lcpValue);
  const totalTime = Date.now() - startTime;
  
  console.log('📊 Results:');
  console.log(`   LCP: ${Math.round(lcpValue)}ms`);
  console.log(`   Total Load: ${totalTime}ms`);
  
  if (lcpValue < 1500) {
    console.log('✅ Excellent LCP (< 1.5s)');
  } else if (lcpValue < 2500) {
    console.log('⚠️  Needs improvement (1.5s - 2.5s)');
  } else {
    console.log('❌ Poor LCP (> 2.5s)');
  }

  await browser.close();
  return { lcp: lcpValue, totalTime };
}

// Run the measurement
if (require.main === module) {
  measureLCP().catch(console.error);
}

module.exports = { measureLCP };
