import type { <PERSON>ada<PERSON> } from 'next'
import { <PERSON> } from 'next/font/google'
import './globals.css'
import 'highlight.js/styles/github-dark.css'
import PerformanceMonitor from '@/components/performance/PerformanceMonitor'
import GoogleAnalytics from '@/components/analytics/GoogleAnalytics'
import SchemaMarkup from '@/components/seo/SchemaMarkup'

// Optimize font loading with Next.js font optimization
const barlow = Barlow({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  display: 'swap', // Use font-display: swap for better LCP
  preload: true,
  variable: '--font-barlow',
})

export const metadata: Metadata = {
  title: {
    default: 'Navhaus | What matters, made real',
    template: 'Navhaus | %s'
  },
  description: 'Navhaus builds custom WordPress websites and web applications. Clean, fast, and scalable development for businesses that value quality.',
  keywords: [
    'navhaus',
    'custom wordpress website',
    'wordpress agency',
    'web development agency',
    'custom wordpress development',
    'wordpress website design',
    'wordpress web development',
    'custom web development',
    'wordpress specialists',
    'wordpress experts',
    'web design agency',
    'wordpress consulting'
  ],
  authors: [{ name: 'Navhaus' }],
  creator: 'Navhaus',
  publisher: 'Navhaus',
  metadataBase: new URL('https://navhaus.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'Navhaus | What matters, made real',
    description: 'Navhaus builds custom WordPress websites and web applications. Clean, fast, and scalable development for businesses that value quality.',
    url: 'https://navhaus.com',
    siteName: 'Navhaus',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Navhaus - What matters, made real',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Navhaus | What matters, made real',
    description: 'Navhaus builds custom WordPress websites and web applications. Clean, fast, and scalable development for businesses that value quality.',
    images: ['/images/og-image.png'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    shortcut: '/favicon.ico',
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  manifest: '/site.webmanifest',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={barlow.variable}>
      <head>
        {/* DNS prefetch for any external resources */}
        <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
        <link rel="dns-prefetch" href="https://www.googletagmanager.com" />

        {/* Additional favicon declarations for better browser support */}
        <link rel="icon" type="image/svg+xml" href="/images/icon.svg" />
        <link rel="icon" type="image/png" sizes="96x96" href="/favicon-96x96.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="apple-mobile-web-app-title" content="Navhaus" />
        <meta name="theme-color" content="#434897" />
        <meta name="msapplication-TileColor" content="#434897" />

        <GoogleAnalytics />

        {/* Schema Markup for SEO */}
        <SchemaMarkup type="organization" />
        <SchemaMarkup type="localBusiness" />
      </head>
      <body className={barlow.className}>
        <PerformanceMonitor />
        {children}
      </body>
    </html>
  )
}
