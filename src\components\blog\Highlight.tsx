interface HighlightProps {
  children: React.ReactNode
  variant?: 'default' | 'warning' | 'success' | 'info'
}

export default function Highlight({ children, variant = 'default' }: HighlightProps) {
  const variantStyles = {
    default: 'bg-bauhaus-yellow bg-opacity-20 border-bauhaus-yellow',
    warning: 'bg-bauhaus-red bg-opacity-20 border-bauhaus-red',
    success: 'bg-green-100 border-green-400',
    info: 'bg-bauhaus-blue bg-opacity-20 border-bauhaus-blue'
  }

  return (
    <div className={`highlight border-l-4 px-6 py-4 my-8 rounded-r-lg ${variantStyles[variant]}`}>
      <div className="text-bauhaus-black font-medium">
        {children}
      </div>
    </div>
  )
}
