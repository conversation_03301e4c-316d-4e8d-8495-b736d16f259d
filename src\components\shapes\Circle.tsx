interface CircleProps {
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'red' | 'yellow' | 'blue' | 'black' | 'white'
  className?: string
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-16 h-16', 
  lg: 'w-24 h-24',
  xl: 'w-32 h-32'
}

const colorClasses = {
  red: 'bg-brand-red',
  yellow: 'bg-brand-yellow',
  blue: 'bg-brand-blue',
  black: 'bg-black',
  white: 'bg-white border border-black'
}

export default function Circle({
  size = 'md',
  color = 'red',
  className = ''
}: CircleProps) {
  return (
    <div
      className={`rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
    />
  )
}

// Soft Circle with organic feel (inspired by logo roundedness)
export function SoftCircle({
  size = 'md',
  color = 'red',
  className = ''
}: CircleProps) {
  return (
    <div
      className={`rounded-full ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
      style={{ filter: 'blur(0.5px)' }}
    />
  )
}
