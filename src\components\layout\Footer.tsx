'use client'

import Image from 'next/image'
import Link from 'next/link'
import {
  AnimatedSoftCircle,
  AnimatedRoundedRectangle,
  AnimatedTriangle,
  AnimatedSoftGrid,
  AnimatedBlob,
  AnimatedPill,
  AnimatedQuarterCircle
} from '@/components/shapes/AnimatedShapes'

export default function Footer() {
  return (
    <footer className="relative w-full bg-brand-background text-bauhaus-black overflow-hidden">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 opacity-20">
        <AnimatedSoftGrid
          className="w-full h-full text-black"
          opacity="default"
          animationPreset="drift"
          animationIndex={200}
        />
      </div>

      {/* Main Footer Content */}
      <div className="relative z-10 px-6 md:px-12 lg:px-24 py-16 md:py-20">
        <div className="max-w-7xl mx-auto">

          {/* Top Section - Logo, Navigation, and CTA */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-12 lg:gap-16 mb-16">

            {/* Logo and Tagline */}
            <div className="lg:col-span-5 space-y-6">
              <div className="relative">
                <Image
                  src="/images/logo.png"
                  alt="Navhaus"
                  width={140}
                  height={45}
                  className="h-10 w-auto"
                />
                {/* Decorative accent */}
                <div className="absolute -top-2 -right-8">
                  <AnimatedSoftCircle
                    size="sm"
                    color="red"
                    className="w-4 h-4"
                    animationPreset="gentle"
                    animationIndex={201}
                  />
                </div>
              </div>
              <div className="space-y-4">
                <p className="text-xl font-bold text-bauhaus-black">
                  What matters, made real.
                </p>
                <p className="text-gray-700 leading-relaxed max-w-sm">
                  We build bold, efficient, and meaningful digital experiences.
                  Nothing more, nothing less.
                </p>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="lg:col-span-3 lg:mt-16">
              <div className="space-y-6">
                <div>
                  <h3 className="font-bold text-lg mb-3 text-bauhaus-black">Services</h3>
                  <nav className="space-y-2">
                    <Link href="/services" className="block text-gray-700 hover:text-bauhaus-red transition-colors duration-200" title="WordPress and web development services">
                      Web Development Services
                    </Link>
                    <Link href="/contact" className="block text-gray-700 hover:text-bauhaus-red transition-colors duration-200" title="Get a quote for your project">
                      Get a Quote
                    </Link>
                  </nav>
                </div>
                <div>
                  <h3 className="font-bold text-lg mb-3 text-bauhaus-black">Company</h3>
                  <nav className="space-y-2">
                    <Link href="/" className="block text-gray-700 hover:text-bauhaus-red transition-colors duration-200">
                      Home
                    </Link>
                    <Link href="/about" className="block text-gray-700 hover:text-bauhaus-red transition-colors duration-200" title="About our WordPress agency team">
                      About
                    </Link>
                    <Link href="/blog" className="block text-gray-700 hover:text-bauhaus-red transition-colors duration-200" title="WordPress and web development blog">
                      Blog
                    </Link>
                    <Link href="/contact" className="block text-gray-700 hover:text-bauhaus-red transition-colors duration-200" title="Contact WordPress developers">
                      Contact
                    </Link>
                  </nav>
                </div>
              </div>
            </div>

            {/* CTA Section */}
            <div className="lg:col-span-4 space-y-8 lg:mt-16">
              <div className="space-y-6">
                <p className="text-bauhaus-black font-medium">
                  Ready to build something?
                </p>
                <Link
                  href="/contact"
                  className="inline-block px-4 py-2 border-2 border-bauhaus-black bg-transparent text-bauhaus-black font-bold uppercase tracking-wide hover:bg-bauhaus-black hover:text-brand-background transition-colors duration-200 rounded-xl text-sm"
                >
                  Start here
                </Link>
              </div>

              {/* Response Time Indicator */}
              <div className="flex flex-col gap-2 text-sm text-gray-600">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-bauhaus-red rounded-full"></div>
                  <div className="w-2 h-2 bg-bauhaus-yellow rounded-full"></div>
                  <div className="w-2 h-2 bg-bauhaus-blue rounded-full"></div>
                </div>
                <span>Usually responds within 24 hours</span>
              </div>
            </div>
          </div>

          {/* Decorative Divider with Animated Elements */}
          <div className="relative py-8 mb-12">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-full h-px bg-gray-800"></div>
            </div>
            <div className="relative flex justify-center space-x-8">
              <AnimatedRoundedRectangle
                width="lg"
                height="sm"
                color="red"
                className="w-16 h-4"
                animationPreset="flowing"
                animationIndex={205}
              />
              <AnimatedSoftCircle
                size="md"
                color="yellow"
                className="w-8 h-8"
                animationPreset="pulse"
                animationIndex={206}
              />
              <AnimatedTriangle
                size="md"
                color="blue"
                direction="up"
                className="w-8 h-8"
                animationPreset="dynamic"
                animationIndex={207}
              />
            </div>
          </div>

          {/* Bottom Section - Copyright and Credits */}
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-600">
              © {new Date().getFullYear()} Navhaus. All rights reserved.
            </div>

            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <span>Built with intention</span>
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-bauhaus-red rounded-full"></div>
                <div className="w-2 h-2 bg-bauhaus-yellow rounded-full"></div>
                <div className="w-2 h-2 bg-bauhaus-blue rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Background Elements */}
      <div className="absolute top-16 left-16 opacity-15">
        <AnimatedBlob
          color="red"
          className="w-24 h-24"
          animationPreset="drift"
          animationIndex={208}
        />
      </div>
      <div className="absolute bottom-20 right-20 opacity-10">
        <AnimatedQuarterCircle
          color="blue"
          corner="top-left"
          className="w-32 h-32"
          animationPreset="gentle"
          animationIndex={209}
        />
      </div>
      <div className="absolute top-1/2 right-8 opacity-8">
        <AnimatedRoundedRectangle
          width="lg"
          height="xl"
          color="yellow"
          className="w-8 h-24"
          animationPreset="float"
          animationIndex={210}
        />
      </div>
      <div className="absolute bottom-8 left-1/4 opacity-12">
        <AnimatedSoftCircle
          size="lg"
          color="red"
          className="w-16 h-16"
          animationPreset="energetic"
          animationIndex={211}
        />
      </div>
      <div className="absolute top-20 right-1/3 opacity-8">
        <AnimatedTriangle
          size="lg"
          color="yellow"
          direction="up"
          className="w-12 h-12"
          animationPreset="pulse"
          animationIndex={212}
        />
      </div>
    </footer>
  )
}
