/**
 * ICO File Creation Script
 *
 * This script creates a proper multi-size ICO file with 16x16, 32x32, and 48x48 sizes embedded.
 * Run: node scripts/create-ico.js
 */

const sharp = require('sharp');
const fs = require('fs');
const path = require('path');
const { encode } = require('ico-endec');

async function createIcoFile() {
  const svgPath = path.join(__dirname, '../public/images/icon.svg');
  const publicDir = path.join(__dirname, '../public');

  // ICO standard sizes
  const icoSizes = [16, 32, 48];

  console.log('Creating proper multi-size ICO file...');

  try {
    // Generate individual PNG buffers for each size
    const pngBuffers = [];

    for (const size of icoSizes) {
      const buffer = await sharp(svgPath)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 }
        })
        .png()
        .toBuffer();

      pngBuffers.push(buffer);
      console.log(`✓ Generated ${size}x${size} PNG buffer`);
    }

    // Create proper multi-size ICO file using ico-endec
    const icoBuffer = encode(pngBuffers);
    const icoPath = path.join(publicDir, 'favicon.ico');

    fs.writeFileSync(icoPath, icoBuffer);
    console.log('✓ Created favicon.ico with embedded sizes: 16x16, 32x32, 48x48');

    // Get file size for verification
    const stats = fs.statSync(icoPath);
    console.log(`✓ ICO file size: ${stats.size} bytes`);

    console.log('\n🎉 Multi-size ICO file created successfully!');
    console.log('   - Contains 16x16, 32x32, and 48x48 sizes');
    console.log('   - Should pass Real Favicon Generator checks');

  } catch (error) {
    console.error('Error creating ICO file:', error);
    console.log('\n💡 Fallback: Using 48x48 PNG as ICO...');

    // Fallback to single-size ICO
    try {
      const fallbackBuffer = await sharp(svgPath)
        .resize(48, 48, {
          fit: 'contain',
          background: { r: 0, g: 0, b: 0, alpha: 0 }
        })
        .png()
        .toBuffer();

      const icoPath = path.join(publicDir, 'favicon.ico');
      fs.writeFileSync(icoPath, fallbackBuffer);
      console.log('✓ Created fallback favicon.ico (48x48 PNG)');
    } catch (fallbackError) {
      console.error('Fallback also failed:', fallbackError);
    }
  }
}

if (require.main === module) {
  createIcoFile();
}

module.exports = createIcoFile;
